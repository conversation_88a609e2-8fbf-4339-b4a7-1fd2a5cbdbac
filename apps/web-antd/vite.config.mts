import { defineConfig } from '@vben/vite-config';

// 自行取消注释来启用按需导入功能
import { AntDesignVueResolver } from 'unplugin-vue-components/resolvers';
import Components from 'unplugin-vue-components/vite';

import { configCopyPlugin } from './src/plugins/config-copy-plugin';

const config = defineConfig(async () => {
  return {
    application: {},
    vite: {
      build: {
        target: 'es2022', // 支持 top-level await
        rollupOptions: {
          external: (id) => {
            // 外部化所有 Node.js 相关的包，但不包括 @babel/runtime
            return id === 'jiti' ||
                   id.includes('node:') ||
                   id.includes('jiti/') ||
                   (id.includes('babel') && !id.startsWith('@babel/runtime')) ||
                   id.includes('esbuild');
          },
        },
      },
      plugins: [
        // 配置文件复制插件
        configCopyPlugin({
          source: 'public/config.json',
          target: 'config.json',
        }) as any,
        Components({
          dirs: [], // 默认会导入src/components目录下所有组件 不需要
          dts: './types/components.d.ts', // 输出类型文件
          resolvers: [
            AntDesignVueResolver({
              // 需要排除Button组件 全局已经默认导入了
              exclude: ['Button'],
              importStyle: false, // css in js
            }),
          ],
        }),
      ],
      server: {
        proxy: {
          // '/api': {
          //   target: 'http://*************:8081',
          // },
          '/prod-api': {
            changeOrigin: true,
            // rewrite: (path) => path.replace(/^\/prod-api/, ''),
            // mock代理目标地址
            target: 'http://**************:30080',
            // target: 'http://*************:8080',
            // target: 'http://localhost:8080',
            // target: 'http://**************:30080/api',
            // target: 'http://**************:30089',
            // target: 'http://*************',
            // target: 'http://**************:30081',
            // target: 'http://*************:8080',
            // target: 'https://www.jsj-ai.com/',
            ws: true,
          },
        },
      },
    },
  };
});

export default config;
